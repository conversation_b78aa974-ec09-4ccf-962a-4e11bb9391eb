import vue from '@vitejs/plugin-vue'
import viteCompression from "vite-plugin-compression";
import { resolve } from "path";
import eslintPlugin from 'vite-plugin-eslint'
import autoprefixer from "autoprefixer";

const timestamp = new Date().getTime().toString().slice(-8); // * 取后八位字符串

export default ({
    base: '/h5/',
    // 配置需要使用的插件列表
    plugins: [
        vue(),
        viteCompression({
            filter: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
            verbose: true, // 输出压缩进度
            threshold: 10240, // 只有大于10KB的文件才会被压缩
            algorithm: 'gzip', // 使用 gzip 压缩
            ext: '.gz' // 压缩后的文件扩展名
        }),
        // 增加下面的配置项,这样在运行时就能检查 eslint 规范
        eslintPlugin({
            include: ['src/**/*.js', 'src/**/*.vue', 'src/*.js', 'src/*.vue']
        })
    ],
    publicDir: "public", // 默认 public
    resolve: {
        // 配置路径别名
        alias: {
            '@': resolve(__dirname, './src')
        },
    },
    css: {
        // css预处理器
        preprocessorOptions: {
            less: {
                javascriptEnabled: true,
                charset: false,
                additionalData: '@import "@/assets/style/global.less";',
            },
        },
        postcss: {
            plugins: [
                // * 自动添加前缀
                autoprefixer({
                    overrideBrowserslist: [
                        "Android 4.1",
                        "iOS 7.1",
                        "Chrome > 31",
                        "ff > 31",
                        "ie >= 8"
                        //'last 2 versions', // 所有主流浏览器最近2个版本
                    ],
                    grid: true
                })
            ]
        },
        devSourcemap: true // * 开发环境生成css的sourcemap
    },
    build: {
        esbuild: {
            drop: ["console", "debugger"],
        },
        rollupOptions: {
            output: {
                entryFileNames: `assets/app.[hash].${timestamp}.js`,
                chunkFileNames: `assets/js/[hash].${timestamp}.js`, // * 混淆名称优化
                assetFileNames: `assets/[ext]/[hash].${timestamp}.[ext]`,
            }
        },
        //启用/禁用 CSS 代码拆分
        cssCodeSplit: true,
    },
    server: {
        hmr: true,
        host: "0.0.0.0",
        port: 7799,
        open: true,
        proxy: {
            "/down": {
                target: "https://cdn01.rrsjk.com",
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/down/, "")
            },
            "/api/hdsapi": {
                target: "http://operation.xiaoxianglink.com/",
                // target: "http://**********:8096/",
                // target: "http://console.rrsjk.com/",
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, "")
            },
            "/api/merchant": {
                target: "http://operation.xiaoxianglink.com/",
                // target: "http://***********:8096/",
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, "")
            },
            '/api': {
                target: "http://console.rrsjk.com",
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, "")
            }
        }
    }
})

