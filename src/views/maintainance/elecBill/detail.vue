<template>
  <div class="elec-bill-page">
    <el-page-header class="cus-back" icon="ArrowLeft" @back="$router.go(-1)" />
    <!-- 电站信息 -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>电站信息</span>
        </div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="电站编码">{{ formData.stationCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="业主姓名">{{ formData.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ formData.phone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电站模式">{{ detailMode[formData.mode] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="关联资方">{{ pmSpecialFlag[formData.specialFlag] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="所属分中心">{{ formData.subCenterName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="服务商类别">{{ identityType[formData.opType] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="运维商名称">{{ formData.opName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="区域">{{ (formData.provinceName || '') + (formData.cityName || '') +
          (formData.regionName
            || '') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="详细地址">{{ formData.address || '-' }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          {{ getDictLabel('electricity_fee_status', formData.status) }}
        </el-descriptions-item>
        <el-descriptions-item label="光E宝/银联签约状态">{{ eSignStatus[formData.signStatus] || '-' }}</el-descriptions-item>
        <el-descriptions-item label="终止状态">{{ stopStatusExtend[formData.stopStatus] || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 历史电费记录 -->
    <el-card class="section-card">
      <div style="margin-bottom: 8px;">
        <el-date-picker
          v-model="searchForm.billYear"
          type="year"
          placeholder="请选择年份"
          value-format="YYYY"
          clearable
        /> <el-button type="primary" @click="getList()">查询</el-button>
      </div>
      <el-table :data="listArr" :height="520" v-loading="loading" empty-text="暂无历史电费记录">
        <el-table-column fixed align="center" type="index" label="序号" width="60" />
        <el-table-column align="center" prop="elecNo" label="发电户号" :width="140">
          <template #default="scope">
            {{ scope.row.elecNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="billDate" label="电费日期" :width="120">
          <template #default="scope">
            {{ scope.row.billDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="elec" label="发电量(kWh)" :width="120">
          <template #default="scope">
            {{ scope.row.elec || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="elecPrice" label="电费单价(元)" :width="120">
          <template #default="scope">
            {{ scope.row.elecPrice || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="receivableAmount" label="应收电费(元)" :width="120">
          <template #default="scope">
            {{ scope.row.receivableAmount || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="actualAmount" label="实收电费(元)" :width="120">
          <template #default="scope">
            {{ scope.row.actualAmount || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="returnRate" label="回款率" :width="80">
          <template #default="scope">
            {{ scope.row.returnRate || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="status" label="状态" :width="120">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'NORMAL' ? 'primary' : 'danger'">{{ getDictLabel('electricity_fee_status', scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="supplementAmount" label="补充电费" :width="100">
          <template #default="scope">
            {{ scope.row.supplementAmount || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" label="备注" :min-width="160">
          <template #default="scope">
            {{ scope.row.remark || '-' }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="160">
            <template #default="scope">
              <el-button link type="primary" @click="goDetail(scope.row)">提报工单</el-button>
              <el-button link type="primary" @click="handleOpenRemarkDialog(scope.row)">备注</el-button>
            </template>
          </el-table-column>
      </el-table>
      <el-pagination
        class="cus-pages"
        v-if="total > 0"
        background
        layout="sizes, prev, pager, next, ->, total"
        :page-sizes="[20, 30]"
        :page-size="pagination.pageSize"
        :current-page="pagination.pageNum"
        :total="total"
        @size-change="changeSize"
        @current-change="changeCurrent"
      />
    </el-card>

    <RemarkDialog v-if="remarkDialogVisible" v-model:visible="remarkDialogVisible" :data="currentRowData" @success="handleRemarkSuccess" />
    <OperateDialog v-if="operateDialogVisible" v-model:visible="operateDialogVisible" :station-code="searchForm.stationCode" @success="getList" />
  </div>
</template>

<script setup>
import RemarkDialog from './components/RemarkDialog.vue';
import OperateDialog from './components/OperateDialog.vue';
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import API from '@/api/maintainance'
import { useDictStore } from '@/stores/modules/dict'
import _D from '@/edata/_osp_data'
import { useTablePagination } from '@/composables/useTablePagination'

const route = useRoute()
const router = useRouter()
const dictStore = useDictStore();

const pmSpecialFlag = _D.pmSpecialFlag;
const identityType = _D.identityType;
const detailMode = _D.detailMode;
const eSignStatus = _D.eSignStatus;
const stopStatusExtend = _D.stopStatusExtend;
const detStationType = _D.detStationType;

const getDictLabel = (dictType, value) => {
  const dictMap = dictStore.getDictMapByType(dictType);
  return dictMap[value] || '-';
};

const stationInfo = ref({
})

const searchForm = reactive({
  billYear: null,
  stationCode: route.query.stationCode,
})

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  params => API.getStationElecBillOnlyPage(params),
  () => searchForm,
  { manual: true, pageSize: 20 }
);

const formData = ref({
  id: '',
  stationId: '',
  stationCode: '',
  elecNo: '',
  billDate: '',
  billReportTime: '',
  billStatus: null,
  elec: null,
  elecPrice: null,
  receivableAmount: null,
  actualAmount: null,
  personalActualAmount: null,
  spActualAmount: null,
  supplementAmount: null,
  totalElec: null,
  totalReceivableAmount: null,
  totalActualAmount: null,
  totalReturnRate: null,
  status: '',
  remark: '',
  createdAt: '',
  createdBy: '',
  updatedAt: '',
  updatedBy: ''
})

// 获取详情数据
const getDetail = async () => {
  const stationCode = route.query.stationCode
  if (!stationCode) {
    ElMessage.error('缺少标识')
    router.back()
    return
  }

  try {
    const res = await API.getStationElecBillDetail({ stationCode })

    if (res.success) {
      Object.assign(formData.value, res.result)
      if (formData.value.createdAt) {
        formData.value.createdAt = formData.value.createdAt.replace('T', ' ')
      }

      if (formData.value.updatedAt) {
        formData.value.createdAt = formData.value.createdAt.replace('T', ' ')
      }
    } else {
      ElMessage.error(res.error || '获取详情失败')
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情数据时发生错误')
  }
}

// 组件挂载后获取数据
onMounted(() => {
  getDetail()
  dictStore.fetchDict([
    'station_type',
		'electricity_fee_status'
  ]);

  if (route.query.stationCode) {
    searchForm.stationCode = route.query.stationCode;
    getList();
  }
})

const remarkDialogVisible = ref(false);
const operateDialogVisible = ref(false);
const currentRowData = ref({});

const handleOpenRemarkDialog = (row) => {
  currentRowData.value = { ...row };
  remarkDialogVisible.value = true;
};

const handleRemarkSuccess = () => {
  remarkDialogVisible.value = false;
  getList();
};

const goDetail = () => {
  operateDialogVisible.value = true;
};
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_view.less';
@import '@/assets/style/_cus_list.less'; // Added for .cus-pages and potentially .cus-table styles

.elec-bill-page {
  padding: 20px;
  background-color: #f8f9fa;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .section-card {
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-card__header) {
      background-color: #ffffff;
      padding: 12px 20px;
      border-bottom: 1px solid #e9ecef;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    // Descriptions 样式调整
    :deep(.el-descriptions__label) {
      font-weight: normal;
      color: #6c757d;
      background-color: #f8f9fa;
    }

    :deep(.el-descriptions__content) {
      color: #212529;
    }

    :deep(.el-descriptions__cell) {
      padding: 8px 12px;
    }
  }

  .info-label {
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #c0c4cc;
    font-size: 14px;
  }

  // 工单处理表单样式
  .el-form {
    :deep(.el-form-item__label) {
      font-weight: 600;
      color: #495057;
      padding-bottom: 4px;
    }


    :deep(.el-upload--picture-card) {
      width: 150px;
      height: 150px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
      width: 150px;
      height: 150px;
    }
  }


  .action-buttons {
    margin-top: 20px;
    padding: 15px 20px;
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    text-align: right;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }
}

.handle-check-items {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;

  .check-items-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #409EFF;
  }
}
</style>
