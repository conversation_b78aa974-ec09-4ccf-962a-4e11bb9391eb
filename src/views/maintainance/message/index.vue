<template>
  <div class="cus-container">
    <div class="cus-header">
      <el-form :model="formSearch" label-width="80px">
        <div class="form-item-grid" :class="{ 'is-expanded': isFormExpanded }">
          <el-form-item label="标题">
            <el-input v-model="formSearch.subject" clearable placeholder="输入标题" />
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="formSearch.readStatus" clearable placeholder="请选择状态" style="width: 100%">
              <el-option v-for="item in messageStatusOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item label="发送时间">
            <el-date-picker v-model="formSearch.sendTimeRange" type="datetimerange" range-separator="至"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
          </el-form-item>

          <el-form-item label="创建人">
            <el-input v-model="formSearch.createdBy" clearable placeholder="输入创建人" />
          </el-form-item>

          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-button link type="primary" @click="toggleFormExpansion">
              {{ isFormExpanded ? "收起" : "展开" }}
              <el-icon class="el-icon--right">
                <component :is="isFormExpanded ? 'ArrowUp' : 'ArrowDown'
                  " />
              </el-icon>
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="cus-wrap">
      <div class="cus-message">
        <el-tabs v-model="activeSidebarTab" @tab-click="handleSidebarTabClick" class="sidebar-tabs">
          <el-tab-pane v-for="type in messageTypeOptions" :key="type.value" :label="type.label"
            :name="type.value"></el-tab-pane>
        </el-tabs>

        <div class="sidebar-cards-container">
          <el-scrollbar style="height: 100%">
            <div v-if="filteredSidebarMessages.length === 0" class="empty-sidebar-state">
              暂无消息
            </div>
            <el-card v-for="message in filteredSidebarMessages" :key="message.id" class="sidebar-message-card" :class="{
              'is-active':
                selectedMessageDetail &&
                selectedMessageDetail.id === message.id,
            }" @click="displayMessageDetail(message)">
              <template #header>
                <div class="card-header-flex">
                  <span class="card-title" :title="message.subject">{{ message.subject }}</span>
                  <el-tag v-if="message.readStatus === 'UNREAD'" type="danger" size="small" effect="light">未读</el-tag>
                  <el-tag v-else-if="message.readStatus === 'READ'" type="success" size="small" effect="light">已读</el-tag>
                </div>
              </template>
              <div class="card-footer-flex">
                <div class="sender-info">
                  <span class="sender-name">{{
                    message.createdByName || "-"
                  }}
                  </span>
                  <span class="sender-name">{{
                    message.createdAt || "-"
                  }}
                  </span>
                </div>
              </div>
            </el-card>
          </el-scrollbar>
        </div>
        <el-pagination class="sidebar-pages" v-if="total > 0" small background layout="prev, pager, next"
          :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
          @current-change="changeCurrent" />
      </div>
      <div class="wrap">
        <div class="cus-main" ref="mainRef">
          <div v-if="selectedMessageDetail" class="message-detail-view">
            <div class="detail-header">
              <h2 class="detail-subject">
                {{ selectedMessageDetail.subject }}
              </h2>
            </div>
            <div class="detail-meta-info">
              <span class="meta-item">创建人:
                {{
                  selectedMessageDetail.createdByName || "-"
                }}</span>
              <span class="meta-item">创建时间:
                {{
                  (selectedMessageDetail.createdAt &&
                    selectedMessageDetail.createdAt.replace(
                      "T",
                      " "
                    )) ||
                  "-"
                }}</span>
            </div>
            <div class="detail-content-wrapper">
              <div class="detail-content" v-html="selectedMessageDetail.content"></div>
            </div>
          </div>
          <div v-else class="empty-detail-view">
            <el-empty description="请从左侧选择一条消息查看详情" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue"
import _ from "lodash"
import { useRoute } from "vue-router"
import { useTablePagination } from "@/composables/useTablePagination"
import { ElMessage } from "element-plus"
import API from "@/api/maintainance"
import { useDictStore } from "@/stores/modules/dict"

const dictStore = useDictStore()

const currentMessageData = ref({})
const selectedMessageDetail = ref(null)
const isFormExpanded = ref(false)

const formSearch = reactive({
  subject: "",
  messageType: null,
  readStatus: null,
  sendTimeRange: [],
  startTime: "",
  endTime: "",
  createdBy: "",
})

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getMessagePage,
  () => {
    const searchParams = { ...formSearch }
    if (
      searchParams.sendTimeRange &&
      searchParams.sendTimeRange.length === 2
    ) {
      searchParams.startTime = searchParams.sendTimeRange[0]
      searchParams.endTime = searchParams.sendTimeRange[1]
    } else {
      searchParams.startTime = null
      searchParams.endTime = null
    }
    delete searchParams.sendTimeRange
    searchParams.messageType = activeSidebarTab.value
    return searchParams
  },
  { manual: true }
)

const messageTypeOptions = computed(() => dictStore.getDictByType("message_type"))
const messageStatusOptions = computed(() => dictStore.getDictByType("message_read_status"))

const activeSidebarTab = ref("SYSTEM")

const toggleFormExpansion = () => {
  isFormExpanded.value = !isFormExpanded.value
}

const filteredSidebarMessages = computed(() => {
  if (!activeSidebarTab.value) return []
  return listArr.value.filter((message) => {
    return message.messageType === activeSidebarTab.value
  })
})

watch(
  filteredSidebarMessages,
  (newFilteredList) => {
    const isSelectedStillPresent =
      selectedMessageDetail.value &&
      newFilteredList.some((m) => m.id === selectedMessageDetail.value.id)

    if (selectedMessageDetail.value && !isSelectedStillPresent) {
      selectedMessageDetail.value = null
    } else if (
      selectedMessageDetail.value &&
      newFilteredList.length === 0
    ) {
      selectedMessageDetail.value = null
    }

    if (!selectedMessageDetail.value && newFilteredList.length > 0) {
      displayMessageDetail(newFilteredList[0])
    }
  },
  { deep: true }
)

const onReset = () => {
  formSearch.subject = ""
  formSearch.messageType = activeSidebarTab.value
  formSearch.readStatus = null
  formSearch.sendTimeRange = []
  formSearch.startTime = null
  formSearch.endTime = null
  formSearch.createdBy = ""
  selectedMessageDetail.value = null
  queryList()
}

const displayMessageDetail = async (message) => {
  selectedMessageDetail.value = _.cloneDeep(message)

  if (message.readStatus === 'UNREAD') {
    try {
      const params = { messageId: message.id }
      const response = await API.markMessageRead(params)
      if (response.data.success) {
        if (
          selectedMessageDetail.value &&
          selectedMessageDetail.value.id === message.id
        ) {
          selectedMessageDetail.value.readStatus = 'READ'
        }
        const itemInList = listArr.value.find(
          (m) => m.id === message.id
        )
        if (itemInList) {
          itemInList.readStatus = 'READ'
        }
      } else {
        ElMessage.error(response.data.error || "标记已读失败")
      }
    } catch (error) {
      console.error("Failed to mark message as read:", error)
      ElMessage.error(error.data.message || "标记已读请求失败")
    }
  }
}

const handleSidebarTabClick = (tab) => {
  formSearch.messageType = tab.paneName
  selectedMessageDetail.value = null
  pagination.pageNum = 1
  queryList()
}

onMounted(async () => {
  await dictStore.fetchDict(["message_type", "message_status", "message_read_status"])
  formSearch.messageType = activeSidebarTab.value
  getList()
})
</script>

<style lang="less" scoped>
@import "@/assets/style/_cus_header.less";
@import "@/assets/style/_cus_list.less";

.wrap {
  height: 100%;
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  padding: 20px;
  box-sizing: border-box;
}

.cus-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  width: 100%;
}

.cus-wrap {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.cus-header {
  margin-bottom: 0px;
  background-color: var(--el-bg-color);
  padding: 20px;
  border-radius: 6px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n + 3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n + 3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto; 
  height: 100%;

  .message-detail-view {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px 20px; 
    background-color: #fff; 

    .detail-header {
      text-align: center; 
      padding-bottom: 15px; 

      .detail-subject {
        margin: 0;
        font-size: 28px; 
        font-weight: 600; 
        color: #2c3e50; 
        line-height: 1.4;
      }
    }

    .detail-meta-info {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;  
      max-width: 800px; 
      margin: 0 auto; 
      width: 100%;
      color: #7f8c8d; 
      font-size: 14px;
      margin-bottom: 25px; 
      border-bottom: 1px solid #eee; 
      padding-bottom: 15px;

      .meta-item {
        display: inline-block;
        margin: 0 10px;
      }
    }

    .detail-content-wrapper {
      flex-grow: 1;
      overflow-y: auto;
      padding: 0 10px; 
      max-width: 800px; 
      margin: 0 auto; 
      width: 100%;

      .detail-content {
        font-size: 17px; 
        line-height: 1.9;
        color: #333; 
        word-wrap: break-word;

        :deep(p) {
          margin-bottom: 1em;
        }

        :deep(img) {
          max-width: 100%;
          height: auto;
          display: block;
          margin: 10px auto;
          border-radius: 4px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        :deep(h1),
        :deep(h2),
        :deep(h3),
        :deep(h4),
        :deep(h5),
        :deep(h6) {
          margin-top: 1.5em;
          margin-bottom: 0.8em;
          font-weight: 600;
        }

        :deep(ul),
        :deep(ol) {
          padding-left: 20px;
          margin-bottom: 1em;
        }

        :deep(li) {
          margin-bottom: 0.5em;
        }

        :deep(blockquote) {
          margin: 1em 0;
          padding: 10px 15px;
          border-left: 4px solid #dfe2e5;
          background-color: #f8f9fa;
          color: #6a737d;
        }
      }
    }
  }

  .empty-detail-view {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: var(--el-text-color-placeholder);
  }
}

.cus-message {
  width: 320px;
  min-width: 300px;
  max-width: 400px;
  border-radius: 6px;
  box-sizing: border-box;
  padding: 12px;
  background-color: var(--el-bg-color);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .sidebar-tabs {
    margin-bottom: 10px;
    flex-shrink: 0;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }

    :deep(.el-tabs__nav-wrap::after) {
      height: 1px;
    }
  }

  .sidebar-cards-container {
    flex: 1;
    overflow: hidden;

    .empty-sidebar-state {
      text-align: center;
      color: var(--el-text-color-placeholder);
      padding-top: 20px;
    }
  }

  .sidebar-pages {
    margin-top: 10px;
    padding: 5px 0;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
  }

  .sidebar-message-card {
    margin-bottom: 10px;
    cursor: pointer;
    transition: box-shadow 0.2s ease-in-out;
    box-shadow: none;

    &.is-active {
      border-left: 3px solid var(--el-color-primary);
      box-shadow: none;
    }

    &:hover {
      box-shadow: var(--el-box-shadow-lighter);
    }

    .card-header-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
      }
    }

    .card-footer-flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: var(--el-text-color-secondary);

      .sender-info {
        display: flex;
        align-items: center;
        gap: 6px;
        justify-content: space-between;
        width: 100%;
      }
    }

    :deep(.el-card__header) {
      padding: 8px 12px;
    }

    :deep(.el-card__body) {
      padding: 10px 12px;
    }
  }
}
</style>
