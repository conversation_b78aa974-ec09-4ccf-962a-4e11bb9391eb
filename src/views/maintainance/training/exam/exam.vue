<template>
  <div class="exam-container" v-loading="loading">
    <div v-if="examData" class="exam-paper">
      <div class="exam-header">
        <h1>{{ examData.name || '试卷' }}</h1>
        <div class="header-info">
          <div class="info-item" v-if="!isPreview">
            <el-icon><Clock /></el-icon>
            <span>剩余时间: {{ formattedTime }}</span>
          </div>
          <div class="info-item">
            <el-icon><Document /></el-icon>
            <span>总分: {{ examData.totalScore || 100 }}分</span>
          </div>
          <div class="info-item">
            <el-icon><Collection /></el-icon>
            <span>总题数: {{ questions.length }}题</span>
          </div>
        </div>
      </div>

      <div class="progress-section">
        <div class="progress-info">
          <div>
            <span>当前进度: {{ currentQuestionIndex + 1 }}/{{ questions.length }}</span>
            <span v-if="!isPreview" style="margin-left: 16px;">已答: {{ answeredCount }}题</span>
          </div>
          <el-select v-model="currentQuestionIndex" placeholder="跳转题目" size="small" style="width: 120px">
            <el-option
              v-for="(item, index) in questions"
              :key="item.id"
              :label="`第 ${index + 1} 题`"
              :value="index"
            />
          </el-select>
        </div>
        <el-progress :percentage="progressPercentage" :stroke-width="10" :show-text="false" />
      </div>

      <div class="question-area" v-if="currentQuestion">
        <div class="question-title">
          <span class="question-number">{{ currentQuestionIndex + 1 }}.</span>
          <div class="question-main">
            <p>{{ currentQuestion.title }}</p>
            <el-tag type="info" size="small">{{ questionTypeLabel }}</el-tag>
          </div>
        </div>

        <div class="question-options">
          <el-radio-group v-if="currentQuestion.type === 'SINGLE'" v-model="userAnswers[currentQuestion.id]" class="option-list" :disabled="isPreview">
            <el-radio
              v-for="option in currentQuestion.options"
              :key="option.optionKey"
              :label="option.optionKey"
              class="option-item"
              size="large"
            >
              <span class="option-label">{{ option.optionKey }}</span>
              <span class="option-content">{{ option.content }}</span>
            </el-radio>
          </el-radio-group>
          <el-checkbox-group v-if="currentQuestion.type === 'MULTIPLE'" v-model="userAnswers[currentQuestion.id]" class="option-list" :disabled="isPreview">
            <el-checkbox
              v-for="option in currentQuestion.options"
              :key="option.optionKey"
              :label="option.optionKey"
              class="option-item"
              size="large"
            >
              <span class="option-label">{{ option.optionKey }}</span>
              <span class="option-content">{{ option.content }}</span>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>

      <div class="exam-footer">
        <el-button @click="goToPrev" :disabled="currentQuestionIndex === 0">
          <el-icon><ArrowLeft /></el-icon>
          上一题
        </el-button>
        <el-button type="primary" @click="goToNext" v-if="currentQuestionIndex < questions.length - 1">
          下一题
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <el-button type="success" @click="submitExam" v-else-if="!isPreview">
          <el-icon><Select /></el-icon>
          交卷
        </el-button>
        <el-button @click="handleClose">
          关闭
        </el-button>
      </div>
    </div>
    <el-empty v-else-if="!loading" description="无法加载试卷" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Clock, Document, Collection, ArrowLeft, ArrowRight, Select } from '@element-plus/icons-vue';
import API from '@/api/maintainance';

const route = useRoute();
const router = useRouter();

const isPreview = ref(false);
const loading = ref(true);
const examData = ref(null);
const questions = ref([]);
const currentQuestionIndex = ref(0);
const userAnswers = ref({});

const examDuration = ref(3600); // 默认60分钟
const remainingTime = ref(examDuration.value);
let timerInterval = null;

onMounted(async () => {
  const examId = route.query.examId;
  const answerId = route.query.answerId;
  isPreview.value = route.query.preview === 'true';

  if (isPreview.value) {
    if (!examId) {
      ElMessage.error('未找到考试ID');
      loading.value = false;
      setTimeout(() => router.back(), 2000);
      return;
    }
    await fetchExamPaper(examId);
  } else {
    if (!answerId) {
      ElMessage.error('未找到答题ID');
      loading.value = false;
      setTimeout(() => router.back(), 2000);
      return;
    }
    await fetchExamAnswer(answerId);
  }
});

onUnmounted(() => {
  clearInterval(timerInterval);
});

const fetchExamPaper = async (id) => {
  loading.value = true;
  try {
    const res = await API.getExamPaper({ examId: id });
    if (res.success && res.result) {
      examData.value = res.result;
      if (res.result.questionData) {
        questions.value = JSON.parse(res.result.questionData);
        initializeAnswers();
      } else {
        questions.value = [];
      }
      if (res.result.duration) {
        examDuration.value = res.result.duration * 60;
        remainingTime.value = examDuration.value;
      }
    } else {
      ElMessage.error(res.error || '获取试卷详情失败');
    }
  } catch (error) {
    ElMessage.error('请求试卷详情时出错');
  } finally {
    loading.value = false;
  }
};

const fetchExamAnswer = async (answerId) => {
  loading.value = true;
  try {
    const res = await API.getExamAnswerDetail({ answerId });
    if (res.success && res.result) {
      examData.value = {
        name: res.result.examName,
        duration: res.result.duration,
        ...res.result
      };

      if (res.result.duration && res.result.startTime) {
        examDuration.value = res.result.duration * 60;
        const startTime = new Date(res.result.startTime).getTime();
        const now = Date.now();
        const elapsedSeconds = Math.floor((now - startTime) / 1000);
        remainingTime.value = Math.max(0, examDuration.value - elapsedSeconds);
      }

      questions.value = res.result.questions;
      
      initializeAnswers(res.result.answers || {});
      
      if (remainingTime.value > 0) {
        startTimer();
      }
    } else {
      ElMessage.error(res.error || '获取考试详情失败');
    }
  } catch (error) {
    console.log(error);
    ElMessage.error('请求考试详情时出错');
  } finally {
    loading.value = false;
  }
};

const initializeAnswers = (existingAnswers = {}) => {
  const answers = {};
  questions.value.forEach(q => {
    const existing = existingAnswers[q.id];
    if (q.type === 'MULTIPLE') {
      answers[q.id] = existing ? existing.split(',').filter(Boolean) : [];
    } else {
      answers[q.id] = existing || null;
    }
  });
  userAnswers.value = answers;
};

const startTimer = () => {
  timerInterval = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--;
    } else {
      clearInterval(timerInterval);
      ElMessage.warning('考试时间到，已自动交卷');
      submitExam();
    }
  }, 1000);
};

const formattedTime = computed(() => {
  const minutes = Math.floor(remainingTime.value / 60);
  const seconds = remainingTime.value % 60;
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
});

const answeredCount = computed(() => {
    return Object.values(userAnswers.value).filter(answer => {
        if (Array.isArray(answer)) {
            return answer.length > 0;
        }
        return answer !== null;
    }).length;
});

const progressPercentage = computed(() => {
  if (questions.value.length === 0) return 0;
  return ((currentQuestionIndex.value + 1) / questions.value.length) * 100;
});

const currentQuestion = computed(() => {
  return questions.value[currentQuestionIndex.value] || null;
});

const questionTypeLabel = computed(() => {
  if (!currentQuestion.value) return '';
  const typeMap = {
    'SINGLE': '单选题',
    'MULTIPLE': '多选题',
  };
  return typeMap[currentQuestion.value.type] || '未知题型';
});

const goToPrev = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--;
  }
};

const goToNext = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    currentQuestionIndex.value++;
  }
};

const submitExam = () => {
  ElMessageBox.confirm('确认要交卷吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    clearInterval(timerInterval);
    loading.value = true;
    const answerData = Object.keys(userAnswers.value).reduce((acc, key) => {
      const answer = userAnswers.value[key];
      if(Array.isArray(answer)) {
        acc[key] = answer.join(',');
      } else {
        acc[key] = answer;
      }
      return acc;
    }, {});
    const submission = {
        id: route.query.answerId,
        answerData,
    };
    try {
      const res = await API.submitExamAnswer(submission);
      if (res.data.success) {
        ElMessage.success('交卷成功！');
        router.push({
          params: '/maintainance/exam/score',
          query: {
            answerId: route.query.answerId
          }
        });
      } else {
        ElMessage.error(res.data.error || '提交试卷失败');
      }
    } catch (error) {
        ElMessage.error('提交试卷时出错');
    } finally {
        loading.value = false;
    }
  }).catch(() => {});
};

const handleClose = () => {
  if (isPreview.value) {
    router.back();
    return;
  }
  ElMessageBox.confirm('确定要退出考试吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const answerData = Object.keys(userAnswers.value).reduce((acc, key) => {
      const answer = userAnswers.value[key];
      if(Array.isArray(answer)) {
        acc[key] = answer.join(',');
      } else {
        acc[key] = answer;
      }
      return acc;
    }, {});
    const submission = {
        id: route.query.answerId,
        answerData,
    };
    API.updateExamAnswer(submission)
    router.back();
  }).catch(() => {});
};
</script>

<style lang="less" scoped>
.exam-container {
  background-color: #f4f4f5;
  height: 100%;
  box-sizing: border-box;
}

.exam-paper {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 24px;

  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #000000;
    margin: 0;
  }

  .header-info {
    display: flex;
    gap: 24px;
    color: #4b5563;
    font-size: 16px;

    .info-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

.progress-section {
  padding: 16px 0;

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #4b5563;
    font-size: 14px;
    margin-bottom: 10px;
  }
}

.question-area {
  padding: 24px 0;
  min-height: 400px;
  flex: 1;
  overflow-y: auto;
}

.question-title {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 24px;

  .question-number {
    font-size: 18px;
    font-weight: 500;
    color: #000000;
  }

  .question-main {
    p {
      font-size: 18px;
      font-weight: 500;
      color: #000000;
      margin: 0 0 8px 0;
    }
  }
}

.question-options {
  .option-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .option-item {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 16px;
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    margin-right: 0;

    &.is-checked {
      border-color: #409eff;
      background-color: #eff6ff;
    }
  }

  :deep(.el-radio__input),
  :deep(.el-checkbox__input) {
    display: none;
  }

  :deep(.el-radio__label),
  :deep(.el-checkbox__label) {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    color: #374151;
    font-weight: normal;
  }

  .option-label {
    width: 32px;
    height: 32px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
  }

  .option-item.is-checked .option-label {
    border-color: #409eff;
    color: #409eff;
  }
}

.exam-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}
</style>
