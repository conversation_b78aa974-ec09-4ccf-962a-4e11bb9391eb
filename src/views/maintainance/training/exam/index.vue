<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :model="searchForm" label-width="80px">
        <!-- 基础查询条件 -->
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="试卷名称">
            <el-input v-model="searchForm.name" placeholder="输入试卷名称" clearable />
          </el-form-item>

          <el-form-item label="考试状态">
            <el-select v-model="searchForm.status" placeholder="选择考试状态" clearable style="width: 100%;">
              <el-option v-for="item in examStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <!-- 展开的查询条件 -->
          <el-form-item label="创建人">
            <el-input v-model="searchForm.createdBy" placeholder="输入创建人" clearable />
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%;"
            />
          </el-form-item>

          <!-- 查询按钮组 -->
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>
    
    <div class="cus-main" ref="mainRef">
      <div class="cus-list" v-loading="loading" ref="cusListRef">
        <div style="text-align: right;">
          <el-button type="success" plain @click="handleAdd">新增试卷</el-button>
        </div>
        
        <el-table :data="listArr" class="cus-table">
          <el-table-column fixed align="center" type="index" label="序号" width="60" />
          <el-table-column fixed align="center" prop="name" label="试卷名称" min-width="200">
            <template #default="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="status" label="考试状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">
                {{ getExamStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="startTime" label="开始时间" width="180">
            <template #default="scope">
              {{ scope.row.startTime && scope.row.startTime.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="endTime" label="结束时间" width="180">
            <template #default="scope">
              {{ scope.row.endTime && scope.row.endTime.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="duration" label="考试时长(分钟)" width="120">
            <template #default="scope">
              {{ scope.row.duration || '-' }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="passScore" label="及格分数" width="100">
            <template #default="scope">
              {{ scope.row.passScore || '-' }}
            </template>
          </el-table-column> -->
          <el-table-column align="center" prop="questionCount" label="总题数" width="100">
            <template #default="scope">
              {{ scope.row.questionCount || 0 }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="totalScore" label="总分数" width="100">
            <template #default="scope">
              {{ scope.row.totalScore || 0 }}
            </template>
          </el-table-column> -->
          <el-table-column align="center" prop="createdBy" label="创建人" width="120">
            <template #default="scope">
              {{ scope.row.createdBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="createdAt" label="创建时间" width="180">
            <template #default="scope">
              {{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" align="center" label="操作" width="160">
            <template #default="scope">
              <el-button v-if="scope.row.status === 'DRAFT'" link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button 
                v-if="scope.row.status === 'DRAFT'" 
                link 
                type="success" 
                @click="handlePublish(scope.row)"
              >
                发布
              </el-button>
              <!-- <el-button 
                v-if="scope.row.status === 'PUBLISHED'" 
                link 
                type="warning" 
                @click="handleEnd(scope.row)"
              >
                结束
              </el-button> -->
              <el-button v-if="scope.row.status === 'DRAFT'"  link type="danger" @click="handleDelete(scope.row)">删除</el-button>
              <el-button v-if="scope.row.status !== 'DRAFT'" type="primary" link @click="handleView(scope.row)">查看试卷</el-button>
              <el-button v-if="scope.row.status !== 'DRAFT'" type="primary" link @click="goToStatistic(scope.row)">考试统计</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
          @size-change="changeSize" @current-change="changeCurrent" />
      </div>
    </div>
    
    <Operate v-model:visible="operateVisible" :data="operateData" @success="handleOperateSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive, onActivated } from 'vue';
import { useTablePagination } from '@/composables/useTablePagination';
import API from '@/api/maintainance';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDictStore } from '@/stores/modules/dict';
import Operate from './components/Operate.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 展开收缩状态
const isExpanded = ref(false);

// 字典store
const dictStore = useDictStore();

// 搜索表单
const searchForm = reactive({
  name: '',
  status: '',
  createdBy: '',
  createTimeRange: []
});

// 使用组合式函数管理表格和分页状态
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getExamPage,
  () => {
    const params = { ...searchForm };
    if (params.createTimeRange && params.createTimeRange.length === 2) {
      params.createTimeStart = params.createTimeRange[0];
      params.createTimeEnd = params.createTimeRange[1];
    }
    delete params.createTimeRange;
    return params;
  },
  { manual: true }
);

// 操作弹窗相关
const operateVisible = ref(false);
const operateData = ref({});

// 考试状态选项
const examStatusOptions = [
  { label: '未发布', value: 'DRAFT' },
  { label: '已发布', value: 'PUBLISHED' },
  { label: '已结束', value: 'ENDED' }
];

// 获取考试状态标签
const getExamStatusLabel = (status) => {
  const statusMap = {
    'DRAFT': '未发布',
    'PUBLISHED': '已发布',
    'ENDED': '已结束'
  };
  return statusMap[status] || status;
};

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'DRAFT': 'info',
    'PUBLISHED': 'success',
    'ENDED': 'warning'
  };
  return typeMap[status] || 'info';
};

// 重置搜索表单
const onReset = () => {
  searchForm.name = '';
  searchForm.status = '';
  searchForm.createdBy = '';
  searchForm.createTimeRange = [];
  queryList();
};

// 切换展开收缩
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 新增试卷
const handleAdd = () => {
  operateData.value = {};
  operateVisible.value = true;
};

// 编辑试卷
const handleEdit = (row) => {
  operateData.value = { ...row };
  operateVisible.value = true;
};

// 发布试卷
const handlePublish = (row) => {
  ElMessageBox.confirm('确认要发布该试卷吗？发布后将不能修改基本信息。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.publishExam({ id: row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('发布成功');
        getList();
      } else {
        ElMessage.error(response.data.error || '发布失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
    // 用户取消发布
  });
};

// 结束考试
const handleEnd = (row) => {
  ElMessageBox.confirm('确认要结束该考试吗？结束后考生将无法继续答题。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.endExam({ id: row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('考试已结束');
        getList();
      } else {
        ElMessage.error(response.data.error || '操作失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
    // 用户取消操作
  });
};

// 删除试卷
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该试卷吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    API.deleteExam({ id: row.id }).then(response => {
      if (response.data.success) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(response.data.error || '删除失败');
      }
    }).catch(error => {
      ElMessage.error(error.message || '请求失败');
    });
  }).catch(() => {
    // 用户取消删除
  });
};

const handleView = (row) => {
  router.push({
    path: '/maintainance/training/exam/do',
    query: {
      preview: true,
      examId: row.id,
    }
  })
}

const goToStatistic = (row) => {
  router.push({
    path: '/maintainance/training/exam/statistic',
    query: {
      examId: row.id
    }
  })
}

// 操作成功回调
const handleOperateSuccess = () => {
  getList();
};

// 页面初始化
onActivated(() => {
  getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  >.el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
