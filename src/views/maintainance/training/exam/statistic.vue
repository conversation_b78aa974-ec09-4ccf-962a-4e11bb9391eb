<template>
  <div class="cus-container">
    <el-page-header class="cus-back" icon="ArrowLeft" @back="$router.go(-1)" />
    <div class="cus-header">
      <el-form :model="formSearch" label-width="80px">
        <div class="form-item-grid" :class="{ 'is-expanded': isFormExpanded }">
                  <el-form-item label="运维商">
            <el-input v-model="formSearch.opName" clearable placeholder="输入运维商名称" />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="formSearch.userRole" placeholder="选择角色" clearable style="width: 100%;">
              <el-option v-for="item in userRoleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="分中心">
            <el-select v-model="formSearch.userSubCenterCodes" placeholder="选择分中心" clearable multiple style="width: 100%;">
              <el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-button link type="primary" @click="toggleFormExpansion">
              {{ isFormExpanded ? "收起" : "展开" }}
              <el-icon class="el-icon--right">
                <component :is="isFormExpanded ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="cus-wrap">
      <div class="cus-message">
        <div class="chart-container">
          <div class="chart-item">
            <div id="passRateChart" class="chart"></div>
            <div class="chart-summary">
              通过率: {{ examStats.passRate }}%
            </div>
          </div>
          <div class="chart-item">
            <div id="scoreDistributionChart" class="chart"></div>
            <div class="chart-summary">
              平均分: {{ examStats.averageScore }}分
            </div>
          </div>
        </div>
      </div>
      <div class="wrap">
        <div class="cus-main" ref="mainRef">
          <div class="cus-list">
            <h3 class="chart-title">考试明细</h3>
            <el-table v-loading="loading" :data="listArr" class="cus-table">
              <el-table-column fixed align="center" type="index" label="序号" width="60" />
              <el-table-column align="center" prop="userName" label="姓名" min-width="100" show-overflow-tooltip />
              <el-table-column align="center" prop="userRole" label="角色" min-width="100">
                <template #default="scope">
                  <span>
                    {{ getDictLabel('user_role', scope.row.userRole) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="opName" label="运维商" min-width="150" show-overflow-tooltip>
                <template #default="scope">
                  <span>
                    {{ scope.row.opName || '-' }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="userSubCenterCode" label="分中心" width="100">
                <template #default="scope">
                  {{ getSubCenterName(scope.row.userSubCenterCode) }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="score" label="分数" min-width="80" />
              <el-table-column align="center" prop="result" label="结果" min-width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.result === 'PASS' ? 'success' : 'danger'" size="small">
                    {{ scope.row.result === 'PASS' ? '通过' : '未通过' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="startTime" label="开始时间" min-width="180" />
              <el-table-column align="center" prop="endTime" label="结束时间" min-width="180" />
            </el-table>
            <el-pagination 
              class="cus-pages" 
              v-if="total" 
              background 
              layout="sizes, prev, pager, next, ->, total"
              :page-sizes="[10, 20, 30]" 
              :page-size="pagination.pageSize" 
              :current-page="pagination.pageNum"
              :total="total" 
              @size-change="changeSize" 
              @current-change="changeCurrent" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from "vue"
import _ from "lodash"
import _D from '@/edata/_osp_data';
import { useTablePagination } from "@/composables/useTablePagination"
import { ElMessage } from "element-plus"
import { useRoute } from 'vue-router';
import API from "@/api/maintainance"
import * as echarts from 'echarts/core'
import { 
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import { PieChart } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useDictStore } from '@/stores/modules/dict';


const dictStore = useDictStore();
const route = useRoute()

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  LabelLayout,
  CanvasRenderer
])

const isFormExpanded = ref(false)

const subCenterOptions = _D.subCenterList;
// 工单统计数据
const examStats = reactive({
  averageScore: 0,
  completedCount: 0,
  failCount: 0,
  maxScore: 0,
  minScore: 0,
  passCount: 0,
  passRate: 0
})

const formSearch = reactive({
  examId: route.query.examId,
  opName: "", // 运维商名称
  userRole: null, // 用户角色
  userSubCenterCodes: [] // 用户分中心编码列表
})

// 重置搜索表单
const onReset = () => {
  formSearch.userRole = null
  formSearch.opName = ""
  formSearch.userSubCenterCodes = []
  queryList()
}

const toggleFormExpansion = () => {
  isFormExpanded.value = !isFormExpanded.value
}

const getDictLabel = (dictType, value) => {
	const dictMap = dictStore.getDictMapByType(dictType);
	return dictMap[value] || '-';
};

const subCenterMap = subCenterOptions.reduce((acc, cur) => {
  acc[cur.value] = cur.label;
  return acc;
}, {});

const getSubCenterName = (value) => {
  return subCenterMap[value] || '-'
}

// 获取考试详情列表数据
const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  changeSize,
  changeCurrent,
} = useTablePagination(
  API.getExamStatisticsPersonDetails,
  () => _.cloneDeep(formSearch),
  { manual: true }
)

// 初始化通过率图表
const initPassRateChart = () => {
  const chartDom = document.getElementById('passRateChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '考试通过率',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      confine: true
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      top: 25,
      itemGap: 30,
      itemWidth: 14,
      itemHeight: 14,
      data: [
        {
          name: '未通过',
          icon: 'rect'
        }, 
        {
          name: '已通过',
          icon: 'rect'
        }
      ],
      textStyle: {
        fontSize: 12
      }
    },
    color: ['#F56C6C', '#67C23A'],
    series: [
      {
        name: '考试通过率',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: examStats.failCount, name: '未通过' },
          { value: examStats.passCount, name: '已通过' }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
  
  // 保存图表实例用于后续resize
  if (!window.chartInstances) {
    window.chartInstances = {};
  }
  window.chartInstances.passRateChart = myChart;
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    myChart.resize()
  })
  
  // 使用ResizeObserver监听容器大小变化
  if (typeof ResizeObserver !== 'undefined') {
    const resizeObserver = new ResizeObserver(() => {
      myChart.resize();
    });
    resizeObserver.observe(chartDom);
  }
}

// 初始化分数分布图表
const initScoreDistributionChart = () => {
  const chartDom = document.getElementById('scoreDistributionChart')
  if (!chartDom) return
  
  const myChart = echarts.init(chartDom)
  const option = {
    title: {
      text: '分数分布',
      left: 'center',
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}分',
      confine: true
    },
    legend: {
      orient: 'horizontal',
      left: 'center',
      top: 25,
      itemGap: 15,
      itemWidth: 14,
      itemHeight: 14,
      data: [
        {
          name: '最低分',
          icon: 'rect'
        },
        {
          name: '平均分',
          icon: 'rect'
        },
        {
          name: '最高分',
          icon: 'rect'
        }
      ],
      textStyle: {
        fontSize: 12
      }
    },
    color: ['#F56C6C', '#E6A23C', '#67C23A'],
    series: [
      {
        name: '分数分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: examStats.minScore, name: '最低分' },
          { value: examStats.averageScore, name: '平均分' },
          { value: examStats.maxScore, name: '最高分' }
        ]
      }
    ]
  }
  
  myChart.setOption(option)
  
  // 保存图表实例用于后续resize
  if (!window.chartInstances) {
    window.chartInstances = {};
  }
  window.chartInstances.scoreDistributionChart = myChart;
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    myChart.resize()
  })
  
  // 使用ResizeObserver监听容器大小变化
  if (typeof ResizeObserver !== 'undefined') {
    const resizeObserver = new ResizeObserver(() => {
      myChart.resize();
    });
    resizeObserver.observe(chartDom);
  }
}

// 初始化图表并更新图表数据
const updateChartsData = async () => {
  try {
    const params = { ...formSearch }
    // 获取考试统计数据
    const res = await API.getExamStatistics(params)
    if (res.success && res.result) {
      const data = res.result
      // 更新考试统计数据
      Object.assign(examStats, data)
      
      // 重新初始化图表
      initPassRateChart()
      initScoreDistributionChart()
    }
  } catch (error) {
    console.error("获取考试统计数据失败:", error)
    ElMessage.error("获取考试统计数据失败")
  }
}


// 手动触发图表resize
const resizeCharts = () => {
  if (window.chartInstances) {
    if (window.chartInstances.passRateChart) {
      window.chartInstances.passRateChart.resize();
    }
    if (window.chartInstances.scoreDistributionChart) {
      window.chartInstances.scoreDistributionChart.resize();
    }
  }
};

// 查询时重新加载图表数据并resize
const queryList = () => {
  pagination.pageNum = 1;
  getList();
  updateChartsData();
  
  // 延迟执行resize，确保DOM已更新
  setTimeout(resizeCharts, 100);
};

// 监听表单展开/收起状态变化，重新调整图表大小
watch(() => isFormExpanded.value, () => {
  setTimeout(resizeCharts, 300);
});

onMounted(async () => {
  // 获取考试统计数据列表
  getList()
  
  // 更新图表数据
  updateChartsData()
  
  // 字典
  dictStore.fetchDict([
    'user_role',
  ]);
  
  // 初始化图表
  setTimeout(() => {
    initPassRateChart()
    initScoreDistributionChart()
  }, 0)
})

// 获取角色选项
const userRoleOptions = computed(() => dictStore.getDictByType('user_role'))
</script>

<style lang="less" scoped>
@import "@/assets/style/_cus_header.less";
@import "@/assets/style/_cus_list.less";

.cus-back {
  padding: 0px;
}

.wrap {
  height: 100%;
  flex: 1;
  overflow: hidden;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  padding: 20px;
  box-sizing: border-box;
}

.cus-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  width: 100%;
}

.cus-wrap {
  display: flex;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

.cus-header {
  margin-bottom: 0px;
  background-color: var(--el-bg-color);
  padding: 20px;
  border-radius: 6px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n + 3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n + 3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto; 
  height: 100%;

  .cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		// 表格自动填充剩余空间
		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		// 分页控件固定在底部
		.cus-pages {
			margin-top: 10px;
		}
	}
}

.cus-message {
  width: 320px;
  min-width: 300px;
  max-width: 400px;
  border-radius: 6px;
  box-sizing: border-box;
  padding: 12px;
  background-color: var(--el-bg-color);
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .chart-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 20px;

    .chart-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 15px;
      display: flex;
      flex-direction: column;
      flex: 1;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 15px 0;
        text-align: center;
      }

      .chart {
        flex: 1;
        height: 240px;
      }

      .chart-summary {
        margin-top: 10px;
        text-align: center;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

</style>
