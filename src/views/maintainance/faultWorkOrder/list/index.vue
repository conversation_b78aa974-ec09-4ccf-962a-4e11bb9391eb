<template>
	<div style="height: 100%;">
		<el-tabs v-model="activeTab">
			<el-tab-pane label="未下发" name="TO_DISPATCH"></el-tab-pane>
			<!-- <el-tab-pane label="待指派" name="dispatch"></el-tab-pane> -->
			<el-tab-pane label="待处理" name="TO_PROCESS"></el-tab-pane>
			<el-tab-pane label="已处理" name="HANDLED"></el-tab-pane>
			<el-tab-pane label="待审核" name="TO_AUDIT"></el-tab-pane>
			<el-tab-pane label="已完成" name="FINISHED"></el-tab-pane>
			<el-tab-pane label="已关单" name="CLOSED"></el-tab-pane>
			<el-tab-pane label="全部" name="ALL"></el-tab-pane>
		</el-tabs>
		<div class="wrap">
			<div class="cus-header">
				<el-form :model="formSearch" label-width="100px">
					<!-- 基础查询条件 -->
					<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
						<el-form-item label="运维单号">
							<el-input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
						</el-form-item>

						<el-form-item label="电站编码">
							<el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
						</el-form-item>

						<el-form-item label="运维商">
							<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
						</el-form-item>

						<el-form-item label="电站名称">
							<el-input v-model="formSearch.stationName" placeholder="输入电站名称" clearable />
						</el-form-item>

						<el-form-item label="逆变器SN码">
							<el-input v-model="formSearch.inverterSn" placeholder="输入逆变器SN码" clearable />
						</el-form-item>

						<el-form-item label="分中心">
							<el-select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
								<el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="运维商类别">
							<el-select v-model="formSearch.opType" placeholder="选择运维商类别" clearable style="width: 100%;">
								<el-option v-for="(item, val) in identityType" :label="item" :value="val" :key="val" />
							</el-select>
						</el-form-item>

						<el-form-item label="工单来源">
							<el-select v-model="formSearch.orderSourceList" placeholder="选择工单来源" clearable multiple
								collapse-tags collapse-tags-tooltip style="width: 100%;">
								<el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="工单类型">
							<el-select v-model="formSearch.orderTypeList" placeholder="选择工单类型" clearable multiple
								collapse-tags collapse-tags-tooltip style="width: 100%;">
								<el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item v-if="activeTab === 'ALL'" label="工单状态">
							<el-select v-model="formSearch.orderStatusList" placeholder="选择工单状态" clearable multiple
								collapse-tags collapse-tags-tooltip style="width: 100%;">
								<el-option v-for="item in workOrderStatusOptions" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>

						<el-form-item label="资方所属">
							<el-select v-model="formSearch.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
								<el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label"
									:value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="是否超时">
							<el-select v-model="formSearch.overTime" placeholder="选择是否超时" clearable style="width: 100%;">
								<el-option key="yes" label="是" :value="true" />
								<el-option key="no" label="否" :value="false" />
							</el-select>
						</el-form-item>
						<el-form-item label="生成工单时间">
							<el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间"
								end-placeholder="结束时间" @change="dateChange" clearable style="width: 100%;" />
						</el-form-item>

						<!-- 查询按钮组 -->
						<div class="search-buttons">
							<el-button type="default" @click="onReset">重置</el-button>
							<el-button type="primary" @click="queryList">查询</el-button>
							<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
								{{ isExpanded ? '收起' : '展开' }}
								<el-icon>
									<arrow-up v-if="isExpanded" />
									<arrow-down v-else />
								</el-icon>
							</el-link>
						</div>
					</div>
				</el-form>
			</div>
			<div class="cus-main" ref="mainRef">
				<div class="cus-list" v-loading="loading" ref="cusListRef">
					<div style="text-align: right;">
						<el-button type="success" plain @click="batchDispatch" :loading="batchDispatchLoading" v-if="activeTab === 'TO_DISPATCH'">批量下发</el-button>
						<el-button type="success" plain @click="batchAuditPass" :loading="batchAuditLoading" v-if="activeTab === 'TO_AUDIT'">批量审核通过</el-button>
						<el-button type="primary" v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="下载中..." plain @click="exportList" v-if="activeTab === 'ALL'">导出</el-button>
					</div>
					<el-table :data="listArr" class="cus-table" @selection-change="handleSelectionChange">
						<el-table-column fixed align="center" type="selection" width="55" />
						<el-table-column fixed align="center" type="index" label="序号" width="60" />
						<el-table-column fixed align="center" prop="orderCode" label="运维单号" width="200" />
						<el-table-column align="center" prop="stationCode" label="电站编码" width="200">
							<template #default="scope">
								{{ scope.row.stationCode || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="inverterSn" label="逆变器SN码" width="150">
							<template #default="scope">
								{{ scope.row.inverterSn || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderType" label="工单类型" width="120">
							<template #default="scope">
								{{ getDictLabel('work_order_type', scope.row.orderType) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderSource" label="工单来源" width="120">
							<template #default="scope">
								{{ getDictLabel('work_order_source', scope.row.orderSource) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderStatus" label="工单状态" width="120">
							<template #default="scope">
								{{ getDictLabel('work_order_status', scope.row.orderStatus) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderName" label="故障现象" width="150">
							<template #default="scope">
								{{ scope.row.orderName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="endDate" label="是否超时" width="120">
							<template #default="scope">
								{{ typeof scope.row.overTime === 'boolean' ? (scope.row.overTime ? '是' : '否') : '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationMode" label="模式" width="120">
							<template #default="scope">
								{{ detailMode[scope.row.stationMode] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="specialFlag" label="资方所属" width="120">
							<template #default="scope">
								{{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationType" label="电站类型" width="120">
							<template #default="scope">
								{{ detStationType[scope.row.stationType] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="projectCompanyName" label="所属项目公司" width="150">
							<template #default="scope">
								{{ scope.row.projectCompanyName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opName" label="运维商" width="150">
							<template #default="scope">
								{{ scope.row.opName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opType" label="运维商类别" width="120">
							<template #default="scope">
								{{ identityType[scope.row.opType] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="subCenterName" label="所属分中心" width="120">
							<template #default="scope">
								{{ scope.row.subCenterName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationName" label="电站业主" width="120">
							<template #default="scope">
								{{ scope.row.stationName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationPhone" label="业主联系方式" width="150">
							<template #default="scope">
								{{ scope.row.stationPhone || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="regionName" label="区域" width="120">
							<template #default="scope">
								{{ scope.row.regionName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="address" label="详细地址" width="200">
							<template #default="scope">
								{{ scope.row.address || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="isWarranty" label="是否在质保期" width="120">
							<template #default="scope">
								{{ isWarranty[scope.row.isWarranty] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="businessType" label="运维业务类型" width="150">
							<template #default="scope">
								{{ businessType[scope.row.businessType] || '-' }}
							</template>
						</el-table-column>
						<el-table-column fixed="right" align="center" label="操作" width="160">
							<template #default="scope">
								<el-button link type="primary" @click="viewOrder(scope.row)">查看</el-button>
								<el-button v-if="['TO_HEAD_DISPATCH'].includes(scope.row.orderStatus) && isSubCenter === false" link
									type="primary" @click="dealOrder(scope.row)">审核</el-button>
								<el-button v-if="['TO_SUB_CENTER_DISPATCH'].includes(scope.row.orderStatus) && isSubCenter === true" link type="primary"
									@click="dealOrder(scope.row)">审核</el-button>
								<!-- <el-button v-if="['TO_ASSIGN'].includes(scope.row.orderStatus)" link type="primary"
									@click="dispatchOrder(scope.row)">指派</el-button> -->
								<el-button v-if="['TO_PROCESS'].includes(scope.row.orderStatus)" link type="primary"
									@click="dealOrder(scope.row)">处理</el-button>
								<el-button
									v-if="['TO_HEAD_AUDIT'].includes(scope.row.orderStatus) && isSubCenter === false && activeTab !== 'HANDLED'"
									link type="primary" @click="dealOrder(scope.row)">审核</el-button>
								<el-button
									v-if="['TO_SUB_CENTER_AUDIT'].includes(scope.row.orderStatus) && isSubCenter === true && activeTab !== 'HANDLED'"
									link type="primary" @click="dealOrder(scope.row)">审核</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
						:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
						:total="total" @size-change="changeSize" @current-change="changeCurrent" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import API from '@/api/maintainance';
import _ from 'lodash';
import _D from '@/edata/_osp_data';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useTablePagination } from '@/composables/useTablePagination';
import { useDictStore } from '@/stores/modules/dict';

const batchAuditLoading = ref(false);
const batchDispatchLoading = ref(false);
const selectedWorkOrders = ref([]);
const isExpanded = ref(false);
const activeTab = ref('TO_DISPATCH');
const createTime = ref('');
const router = useRouter();
const dictStore = useDictStore();

const sourceOptions = computed(() => dictStore.getDictByType('work_order_source'));
const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'));
const workOrderStatusOptions = computed(() => dictStore.getDictByType('work_order_status'));

const getDictLabel = (dictType, value) => {
	const dictMap = dictStore.getDictMapByType(dictType);
	return dictMap[value] || '-';
};

const isSubCenter = ref(null);
const fullscreenLoading = ref(false)

const subCenterOptions = _D.subCenterList;
const identityType = _D.identityType;
const capitalBelongOptions = _D.property;
const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const detStationType = _D.detStationType;
const detailMode = _D.detailMode;

const apiMap = {
	TO_DISPATCH: API.getWorkOrderToDispatchPage,
	TO_PROCESS: API.getWorkOrderPage,
	HANDLED: API.getHandledWorkOrderPage,
	FINISHED: API.getWorkOrderPage,
	CLOSED: API.getWorkOrderPage,
	TO_AUDIT: API.getWorkOrderToAuditPage,
	ALL: API.getWorkOrderPage
}

const formSearch = reactive({
	createTimeEnd: null,
	createTimeStart: null,
	orderCode: '',
	stationCode: '',
	opName: '',
	stationName: '',
	inverterSn: '',
	subCenterCode: '',
	opType: '',
	orderSourceList: [],
	orderTypeList: [],
	orderStatusList: [],
	specialFlag: '',
	orderStatusQuery: '',
	overTime: null
});

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	params => {
		if (['TO_PROCESS', 'FINISHED', 'CLOSED'].includes(activeTab.value)) {
			return apiMap[activeTab.value]({ ...params, orderStatusList: [activeTab.value] })
		} else {
			if (activeTab.value === 'HANDLED') {
				return apiMap[activeTab.value]({ ...params, orderStatusList: null })
			} else {
				return apiMap[activeTab.value](params)
			}
		}
	},
	() => formSearch,
	{ manual: true }
);

const dateChange = (e) => {
	if (e) {
		formSearch.createTimeStart = e[0];
		formSearch.createTimeEnd = e[1];
	} else {
		formSearch.createTimeStart = null;
		formSearch.createTimeEnd = null;
	}
};

const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			createTimeEnd: null,
			createTimeStart: null,
			orderCode: '',
			stationCode: '',
			opName: '',
			stationName: '',
			inverterSn: '',
			subCenterCode: '',
			opType: '',
			orderSourceList: [],
			orderTypeList: [],
			orderStatusList: [],
			specialFlag: '',
			orderStatusQuery: '',
			overTime: null
		});
		queryList();
	},
	3000,
	{
		trailing: false
	}
);

// 导出
const	exportList = _.debounce(
	function () {
		ElMessageBox.confirm('请确认是否导出此筛选条件下的列表?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		})
			.then(() => {
					fullscreenLoading.value = true;
					API.exportWorkOrder({...formSearch})
							.then(res => {
									let binaryData = [];
									let link = document.createElement('a');
									let date = new Date().getTime()
									binaryData.push(res);
									link.style.display = 'none';
									link.href = window.URL.createObjectURL(new Blob(binaryData));
									link.setAttribute('download', `运维工单_${date}.xlsx`);
									document.body.appendChild(link);
									link.click();
									document.body.removeChild(link);
							})
							.then(() => {
									fullscreenLoading.value = false
							})
							.catch(() => {
									fullscreenLoading.value = false
									ElMessage.error('导出失败');
							});
			})
			.catch(() => {
					// console.log('取消');
			});
	},
	300
)

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const dealOrder = (row) => {
	router.push({
		path: '/maintainance/faultWorkOrder/detail',
		query: {
			orderCode: row.orderCode
		}
	});
};

const viewOrder = (row) => {
	router.push({
		path: '/maintainance/faultWorkOrder/detail',
		query: {
			orderCode: row.orderCode,
			action: 'view'
		}
	});
};

watch(activeTab, (newVal) => {
	selectedWorkOrders.value = [];
	queryList();
});

const handleSelectionChange = (selection) => {
	selectedWorkOrders.value = selection;
};

const batchAuditPass = async () => {
	if (activeTab.value !== 'TO_AUDIT') {
		ElMessage.warning('请在“待审核”标签页执行此操作。');
		return;
	}
	if (selectedWorkOrders.value.length === 0) {
		ElMessage.warning('请至少选择一个工单进行审核。');
		return;
	}

	try {
		await ElMessageBox.confirm(
			`确定要批量审核通过选中的 ${selectedWorkOrders.value.length} 个工单吗？`,
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		batchAuditLoading.value = true;
		const workOrderIds = selectedWorkOrders.value.map(item => item.orderCode);
		
		const params = _.cloneDeep(workOrderIds);
		const res = await API.batchAuditPassWorkOrder(params);

		if (res.data && res.data.success) {
			ElMessage.success('批量审核通过成功！');
			queryList();
			selectedWorkOrders.value = [];
		} else {
			ElMessage.error(res.data.error || res.data.message || '批量审核失败，请稍后再试。');
		}
	} catch (error) {
		if (error !== 'cancel') {
			console.error('批量审核操作失败:', error);
		}
	} finally {
		batchAuditLoading.value = false;
	}
};

const batchDispatch = async () => {
	if (activeTab.value !== 'TO_DISPATCH') {
		ElMessage.warning('请在“未下发”标签页执行此操作。');
		return;
	}
	if (selectedWorkOrders.value.length === 0) {
		ElMessage.warning('请至少选择一个工单进行下发。');
		return;
	}

	try {
		await ElMessageBox.confirm(
			`确定要批量下发选中的 ${selectedWorkOrders.value.length} 个工单吗？`,
			'提示',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		batchDispatchLoading.value = true;
		const workOrderIds = selectedWorkOrders.value.map(item => item.orderCode);
		
		const res = await API.batchDispatchWorkOrderAppeal(workOrderIds);

		if (res.data && res.data.success) {
			ElMessage.success('批量下发成功！');
			queryList();
			selectedWorkOrders.value = [];
		} else {
			ElMessage.error(res.data.error || res.data.message || '批量下发失败，请稍后再试。');
		}
	} catch (error) {
		if (error !== 'cancel') {
			console.error('批量下发操作失败:', error);
			ElMessage.error('批量下发操作失败');
		}
	} finally {
		batchDispatchLoading.value = false;
	}
};


const checkIsSubCenter = () => {
	API.getIsSubCenterUser()
		.then((res) => {
			isSubCenter.value = res.result;
		})
}

onActivated(() => {
	getList();
});

onMounted(() => {
	checkIsSubCenter();
	dictStore.fetchDict([
		'work_order_type',
		'work_order_status',
		'work_order_source',
		'close_order_reason',
		'audit_reject_reason',
	]);
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
	background-color: var(--el-bg-color);
	padding-left: 12px;
}

.wrap {
	height: calc(100% - 54px);
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		/* 默认状态（收起）- 只显示前两个元素和按钮组 */
		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		/* 展开状态 - 显示所有元素 */
		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
