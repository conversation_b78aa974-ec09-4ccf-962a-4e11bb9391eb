<template>
	<el-dialog v-model="dialogVisible" title="工单申诉审核" width="600px" :before-close="handleClose" destroy-on-close>
		<div v-if="appealInfo">
			<el-form ref="auditFormRef" :model="auditForm" :rules="auditRules" label-width="100px">
				<el-form-item label="申诉描述:">
					<div>{{ appealInfo.appealDescription || '-' }}</div>
				</el-form-item>
				<el-form-item label="申诉材料:">
					<div v-if="appealInfo.appealUrls && appealInfo.appealUrls.length > 0">
						<el-image v-for="(url, index) in appealInfo.appealUrls" :key="index"
							style="width: 100px; height: 100px; margin-right: 10px;" :src="url"
							:preview-src-list="appealInfo.appealUrls" :initial-index="index" fit="cover" />
					</div>
					<div v-else>-</div>
				</el-form-item>
				<el-form-item label="审核结果" prop="appealStatus">
					<el-select v-model="auditForm.appealStatus" placeholder="请选择审核结果" style="width: 100%;">
						<el-option label="审核通过" value="AUDIT_OK"></el-option>
						<el-option label="审核驳回" value="AUDIT_REJECT"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="审核备注" prop="auditRemark">
					<el-input v-model="auditForm.auditRemark" type="textarea" :rows="3" placeholder="请输入审核备注"></el-input>
				</el-form-item>
			</el-form>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose" :disabled="submitting">取消</el-button>
				<el-button type="primary" :loading="submitting" @click="submitAudit">审核</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import API from '@/api/maintainance';
import _ from 'lodash';
import { ElMessage } from 'element-plus';

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	orderCode: {
		type: String,
		default: '',
	},
});

const emit = defineEmits(['update:visible', 'success']);

const dialogVisible = ref(false);
const auditFormRef = ref(null);
const auditForm = reactive({
	appealStatus: '',
	auditRemark: '',
	orderCode: '',
});

const auditRules = computed(() => {
	return {
		appealStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
		auditRemark: [
			{
				required: auditForm.appealStatus === 'AUDIT_REJECT',
				message: '审核驳回时请输入审核备注',
				trigger: 'blur',
			},
		],
	};
});
const submitting = ref(false);
const appealData = ref(null);

const appealInfo = computed(() => {
	if (appealData.value) {
		return {
			...appealData.value,
			appealUrls: appealData.value.appealUrls ? appealData.value.appealUrls.split(',') : [],
		};
	}
	return null;
});

const fetchAppealData = async (code) => {
    if (!code) return;
    try {
        const res = await API.getWorkOrderAppealList({ orderCode: code });
        if (res.success && res.result && res.result.length > 0) {
            appealData.value = res.result[0];
        } else {
            appealData.value = null;
            ElMessage.error(res.error || '获取申诉信息失败');
        }
    } catch (error) {
        ElMessage.error('获取申诉信息接口调用失败');
    }
}

watch(
	() => props.visible,
	(val) => {
		dialogVisible.value = val;
		if (val && props.orderCode) {
			auditForm.orderCode = props.orderCode;
            fetchAppealData(props.orderCode);
		}
	},
	{ immediate: true }
);

const handleClose = () => {
	if (submitting.value) return;
	emit('update:visible', false);
};

const submitAudit = () => {
	auditFormRef.value.validate((valid) => {
		if (valid) {
			submitting.value = true;
			const params = _.cloneDeep(auditForm);

			API.auditWorkOrderAppeal(params)
				.then((res) => {
					if (res.data.success) {
						ElMessage.success('审核成功');
						emit('success');
						emit('update:visible', false);
					} else {
						ElMessage.error(res.data.error || '审核失败');
					}
				})
				.finally(() => {
					submitting.value = false;
				});
		}
	});
};

watch(
	() => dialogVisible.value,
	(val) => {
		if (!val) {
			if (auditFormRef.value) {
				auditFormRef.value.resetFields();
			}
			appealData.value = null;
			Object.assign(auditForm, {
				appealStatus: '',
				auditRemark: '',
				orderCode: '',
			});
		}
	}
);
</script>

<style lang="less" scoped>
.dialog-footer {
	text-align: right;
}
</style>
