<template>
  <el-dialog title="选择电站" :model-value="visible" width="70%" :close-on-click-modal="false" @close="handleClose">
    <div class="wrap" style="height: 60vh;">
      <div class="cus-header">
        <el-form :model="formSearch" label-width="100px">
          <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
            <el-form-item label="电站编码">
              <el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
            </el-form-item>
            <el-form-item label="业主姓名">
              <el-input v-model="formSearch.name" placeholder="输入租户姓名" clearable />
            </el-form-item>
            <el-form-item label="联系方式">
              <el-input v-model="formSearch.phone" placeholder="输入联系方式" clearable />
            </el-form-item>
            <div class="search-buttons">
              <el-button type="default" @click="onReset">重置</el-button>
              <el-button type="primary" @click="queryList">查询</el-button>
              <el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
                {{ isExpanded ? '收起' : '展开' }}
                <el-icon>
                  <arrow-up v-if="isExpanded" />
                  <arrow-down v-else />
                </el-icon>
              </el-link>
            </div>
          </div>
        </el-form>
      </div>
      <div class="cus-main" ref="mainRef">
        <div class="cus-list" v-loading="loading" ref="cusListRef">
          <el-table :data="listArr" class="cus-table" @row-dblclick="handleRowClick">
            <el-table-column fixed align="center" type="index" label="序号" width="60" />
            <el-table-column align="center" prop="stationCode" label="电站编码" width="150">
              <template #default="scope">
                {{ scope.row.stationCode || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="name" label="业主姓名" width="120">
              <template #default="scope">
                {{ scope.row.name || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="phone" label="联系方式" width="120">
              <template #default="scope">
                {{ scope.row.phone || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="address" label="详细地址" min-width="180" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.address || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="provinceName" label="省份" width="120" show-overflow-tooltip>
                        <template #default="scope">
                            {{ scope.row.provinceName || '-' }}
                        </template>
                    </el-table-column>
            <el-table-column align="center" prop="cityName" label="城市" width="120" show-overflow-tooltip>
                <template #default="scope">
                    {{ scope.row.cityName || '-' }}
                </template>
            </el-table-column>
            <el-table-column align="center" prop="mode" label="模式" width="100">
              <template #default="scope">
                {{ detailMode[scope.row.mode] || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="opName" label="运维服务商" width="150" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.opName || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="power" label="装机功率(kW)" width="120">
              <template #default="scope">
                {{ scope.row.completeConfirmCapacity || '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="createdAt" label="创建日期" width="180">
              <template #default="scope">
                {{ scope.row.createdAt && scope.row.createdAt.replace('T', ' ') || '-' }}
              </template>
            </el-table-column>
          </el-table>
          <el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
            :page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum" :total="total"
            @size-change="changeSize" @current-change="changeCurrent" />
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import API from '@/api/maintainance';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useTablePagination } from '@/composables/useTablePagination';
import _D from '@/edata/_osp_data';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const detailMode = _D.detailMode;
const emit = defineEmits(['update:visible', 'selected']);

const isExpanded = ref(false);
const formSearch = reactive({
  name: '',
  phone: '',
  stationCode: '',
  opName: '',
  mode: ''
});

const { loading, listArr, total, pagination, getList, queryList, changeSize, changeCurrent } = useTablePagination(
  API.getStationPage,
  () => formSearch,
  { manual: true }
);

const onReset = () => {
  formSearch.name = '';
  formSearch.phone = '';
  formSearch.stationCode = '';
  formSearch.opName = '';
  formSearch.mode = '';
  queryList();
};

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const handleClose = () => {
  emit('update:visible', false);
};

const handleRowClick = (row) => {
  emit('selected', row);
  handleClose();
};

watch(() => props.visible, (val) => {
  if (val) {
    getList();
  }
});

onMounted(() => {
  if (props.visible) {
    getList();
  }
});

</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
  padding: 0px;
}

.cus-header {
  margin-bottom: 10px; // 调整间距

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  padding: 0px;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
      cursor: pointer;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>