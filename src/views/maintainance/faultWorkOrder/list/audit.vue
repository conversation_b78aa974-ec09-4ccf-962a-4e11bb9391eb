<template>
	<div style="height: 100%;">
		<AuditAppeal v-if="auditAppealVisible" :visible="auditAppealVisible" :order-code="currentRow.orderCode"
			@update:visible="auditAppealVisible = false" @success="handleAuditSuccess" />
		<el-tabs v-model="activeTab">
			<el-tab-pane label="待审核" name="pending"></el-tab-pane>
			<el-tab-pane label="已审核" name="audited"></el-tab-pane>
		</el-tabs>
		<div class="wrap">
			<div class="cus-header">
				<el-form :model="formSearch" label-width="100px">
					<!-- 基础查询条件 -->
						<div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
							<el-form-item label="运维单号">
							<el-input v-model="formSearch.orderCode" placeholder="输入运维单号" clearable />
							</el-form-item>

							<el-form-item v-if="activeTab === 'audited'" label="申诉状态">
								<el-select v-model="formSearch.appealStatus" placeholder="选择申诉状态" clearable style="width: 100%;">
									<el-option v-for="item in appealAuditStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="电站编码">
								<el-input v-model="formSearch.stationCode" placeholder="输入电站编码" clearable />
							</el-form-item>

							<el-form-item label="运维商">
								<el-input v-model="formSearch.opName" placeholder="输入运维商" clearable />
							</el-form-item>

							<el-form-item label="电站名称">
								<el-input v-model="formSearch.stationName" placeholder="输入电站名称" clearable />
							</el-form-item>

							<el-form-item label="逆变器SN码">
								<el-input v-model="formSearch.inverterSn" placeholder="输入逆变器SN码" clearable />
							</el-form-item>

							<el-form-item label="分中心">
								<el-select v-model="formSearch.subCenterCode" placeholder="选择分中心" clearable style="width: 100%;">
									<el-option v-for="item in subCenterOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="运维商类别">
								<el-select v-model="formSearch.opType" placeholder="选择运维商类别" clearable style="width: 100%;">
									<el-option v-for="(item, val) in identityType" :label="item" :value="val" :key="val" />
								</el-select>
							</el-form-item>

							<el-form-item label="工单来源">
								<el-select v-model="formSearch.orderSource" placeholder="选择工单来源" clearable style="width: 100%;">
									<el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="工单类型">
								<el-select v-model="formSearch.orderType" placeholder="选择工单类型" clearable style="width: 100%;">
									<el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label"
										:value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item v-if="activeTab === 'ALL'" label="工单状态">
								<el-select v-model="formSearch.orderStatus" placeholder="选择工单状态" clearable style="width: 100%;">
									<el-option v-for="item in workOrderStatusOptions" :key="item.value" :label="item.label"
										:value="item.value" />
								</el-select>
							</el-form-item>

							<el-form-item label="资方所属">
								<el-select v-model="formSearch.specialFlag" placeholder="选择资方所属" clearable style="width: 100%;">
									<el-option v-for="item in capitalBelongOptions" :key="item.value" :label="item.label"
										:value="item.value" />
								</el-select>
							</el-form-item>
							<el-form-item label="是否超时">
								<el-select v-model="formSearch.overTime" placeholder="选择是否超时" clearable style="width: 100%;">
									<el-option key="yes" label="是" :value="true" />
									<el-option key="no" label="否" :value="false" />
								</el-select>
							</el-form-item>
							<el-form-item label="生成工单时间">
								<el-date-picker v-model="createTime" type="daterange" value-format="YYYY-MM-DD" start-placeholder="开始时间"
									end-placeholder="结束时间" @change="dateChange" clearable style="width: 100%;" />
							</el-form-item>
							

							<!-- 查询按钮组 -->
							<div class="search-buttons">
								<el-button type="default" @click="onReset">重置</el-button>
								<el-button type="primary" @click="queryList">查询</el-button>
								<el-link type="primary" @click="toggleExpand" :underline="false" style="margin-left: 10px;">
									{{ isExpanded ? '收起' : '展开' }}
									<el-icon>
										<arrow-up v-if="isExpanded" />
										<arrow-down v-else />
									</el-icon>
								</el-link>
							</div>
						</div>
				</el-form>
			</div>
			<div class="cus-main" ref="mainRef">
				<div class="cus-list" v-loading="loading" ref="cusListRef">
					<!-- <div style="text-align: right;">
						<el-button type="success" plain @click="exportList">导出</el-button>
					</div> -->
					<el-table :data="listArr" class="cus-table">
						<el-table-column fixed align="center" type="index" label="序号" width="60" />
						<el-table-column fixed align="center" prop="orderCode" label="运维单号" width="200" />
						<el-table-column align="center" prop="stationCode" label="电站编码" width="200">
							<template #default="scope">
								{{ scope.row.stationCode || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="inverterSn" label="逆变器SN码" width="150">
							<template #default="scope">
								{{ scope.row.inverterSn || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderType" label="工单类型" width="120">
							<template #default="scope">
								{{ getDictLabel('work_order_type', scope.row.orderType) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderSource" label="工单来源" width="120">
							<template #default="scope">
								{{ getDictLabel('work_order_source', scope.row.orderSource) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderStatus" label="工单状态" width="120">
							<template #default="scope">
								{{ getDictLabel('work_order_status', scope.row.orderStatus) }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="orderName" label="故障现象" width="150">
							<template #default="scope">
								{{ scope.row.orderName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="endDate" label="是否超时" width="120">
							<template #default="scope">
								{{ typeof scope.row.overTime === 'boolean' ? (scope.row.overTime ? '是' : '否') : '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationMode" label="模式" width="120">
							<template #default="scope">
								{{ detailMode[scope.row.stationMode] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="specialFlag" label="资方所属" width="120">
							<template #default="scope">
								{{ pmSpecialFlag[scope.row.specialFlag] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationType" label="电站类型" width="120">
							<template #default="scope">
								{{ detStationType[scope.row.stationType] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="projectCompanyName" label="所属项目公司" width="150">
							<template #default="scope">
								{{ scope.row.projectCompanyName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opName" label="运维商" width="150">
							<template #default="scope">
								{{ scope.row.opName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="opType" label="运维商类别" width="120">
							<template #default="scope">
								{{ identityType[scope.row.opType] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="subCenterName" label="所属分中心" width="120">
							<template #default="scope">
								{{ scope.row.subCenterName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationName" label="电站业主" width="120">
							<template #default="scope">
								{{ scope.row.stationName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="stationPhone" label="业主联系方式" width="150">
							<template #default="scope">
								{{ scope.row.stationPhone || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="regionName" label="区域" width="120">
							<template #default="scope">
								{{ scope.row.regionName || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="address" label="详细地址" width="200">
							<template #default="scope">
								{{ scope.row.address || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="isWarranty" label="是否在质保期" width="120">
							<template #default="scope">
								{{ isWarranty[scope.row.isWarranty] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" prop="businessType" label="运维业务类型" width="150">
							<template #default="scope">
								{{ businessType[scope.row.businessType] || '-' }}
							</template>
						</el-table-column>
						<el-table-column align="center" label="申诉状态" width="120">
							<template #default="scope">
								{{ getDictLabel('appeal_audit_status', scope.row.appealStatus) }}
							</template>
						</el-table-column>
						<el-table-column fixed="right" align="center" label="操作" width="120">
							<template #default="scope">
								<el-button link type="primary" @click="viewOrder(scope.row)">查看</el-button>
								<el-button v-if="['WAIT_AUDIT'].includes(scope.row.appealStatus)" link type="primary" @click="auditOrder(scope.row)">审核</el-button>
							</template>
						</el-table-column>
					</el-table>
					<el-pagination class="cus-pages" v-if="total" background layout="sizes, prev, pager, next, ->, total"
						:page-sizes="[10, 20, 30]" :page-size="pagination.pageSize" :current-page="pagination.pageNum"
						:total="total" @size-change="changeSize" @current-change="changeCurrent" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, onActivated } from 'vue';
import { useDictStore } from '@/stores/modules/dict';
import { useRouter } from 'vue-router';
import API from '@/api/maintainance';
import _ from 'lodash';
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { ElMessageBox } from 'element-plus';
import { useTablePagination } from '@/composables/useTablePagination';
import AuditAppeal from './components/AuditAppeal.vue';
import _D from '@/edata/_osp_data'

const router = useRouter();
const dictStore = useDictStore();
const isExpanded = ref(false);
const activeTab = ref('pending');
const createTime = ref('');
const auditAppealVisible = ref(false);
const currentRow = ref(null);
const sourceOptions = computed(() => dictStore.getDictByType('work_order_source'));
const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'));
const appealAuditStatusOptions = computed(() => dictStore.getDictByType('appeal_audit_status').filter(item => item.value !== 'WAIT_AUDIT'));

const subCenterOptions = _D.subCenterList;
const identityType = _D.identityType;
const capitalBelongOptions = _D.property;
const pmSpecialFlag = _D.pmSpecialFlag;
const businessType = _D.businessType;
const isWarranty = _D.isWarranty;
const detStationType = _D.detStationType;
const detailMode = _D.detailMode;

const apiMap = {
  'pending': API.getPendingWorkOrderAppealPage,
  'audited': API.getAuditedWorkOrderAppealPage,
}

const formSearch = reactive({
	createTimeEnd: null,
	createTimeStart: null,
	orderCode: '',
	stationCode: '',
	opName: '',
	stationName: '',
	inverterSn: '',
	subCenterCode: '',
	opType: '',
	orderSource: '',
	orderType: '',
	specialFlag: '',
	appealStatus: '',
});

const {
	loading,
	listArr,
	total,
	pagination,
	getList,
	queryList,
	changeSize,
	changeCurrent,
} = useTablePagination(
	params =>	{
		return apiMap[activeTab.value]({
			...params,
			appealStatus: activeTab.value === 'audited' ? params.appealStatus : undefined
		})
	},
	() => formSearch,
	{ manual: true }
);

const getDictLabel = (dictType, value) => {
	const dictMap = dictStore.getDictMapByType(dictType);
	return dictMap[value] || '-';
};

const dateChange = (e) => {
	if (e) {
		formSearch.createTimeStart = e[0];
		formSearch.createTimeEnd = e[1];
	} else {
		formSearch.createTimeStart = null;
		formSearch.createTimeEnd = null;
	}
};

const onReset = _.throttle(
	() => {
		createTime.value = '';
		Object.assign(formSearch, {
			createTimeEnd: null,
			createTimeStart: null,
			orderCode: '',
			stationCode: '',
			opName: '',
			stationName: '',
			inverterSn: '',
			subCenterCode: '',
			opType: '',
			orderSource: '',
			orderType: '',
			specialFlag: '',
			appealStatus: ''
		});
		queryList();
	},
	3000,
	{
		trailing: false
	}
);

const exportList = _.throttle(
	() => {
		ElMessageBox.confirm('请确认是否导出此筛选条件下的列表?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			const params = _.cloneDeep(formSearch);
			API.repairReportdoExport(params)
				.then(res => {
					let binaryData = [];
					let link = document.createElement('a');
					binaryData.push(res);
					link.style.display = 'none';
					link.href = window.URL.createObjectURL(new Blob(binaryData));
					link.setAttribute('download', '业主报修列表.xlsx');
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);
				})
		});
	},
	3000,
	{
		trailing: false
	}
);

const toggleExpand = () => {
	isExpanded.value = !isExpanded.value;
};

const viewOrder = (row) => {
	router.push({
		path: '/maintainance/faultWorkOrder/detail',
		query: {
			orderCode: row.orderCode,
			action: 'view'
		}
	});
};

const auditOrder = (row) => {
	currentRow.value = row;
	auditAppealVisible.value = true;
};

const handleAuditSuccess = () => {
	auditAppealVisible.value = false;
	queryList();
};

watch(activeTab, (newVal) => {
	queryList();
});

onMounted(() => {
	dictStore.fetchDict(['appeal_audit_status', 'work_order_source', 'work_order_type', 'work_order_status']);
}) 

onActivated(() => {
	getList();
});
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
	background-color: var(--el-bg-color);
	padding-left: 12px;
}

.wrap {
	height: calc(100% - 54px);
}

.cus-header {
	margin-bottom: 0px;

	.form-item-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 12px;
		width: 100%;

		.el-form-item:nth-child(n+3):not(:last-child) {
			display: none;
		}

		&.is-expanded {
			.el-form-item:nth-child(n+3):not(:last-child) {
				display: flex;
			}
		}
	}

	.search-buttons {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		grid-column: -2 / -1;
	}

	.el-form-item {
		width: 100%;
		margin-bottom: 0px;

		.el-input,
		.el-select,
		.el-date-editor {
			width: 100%;
		}
	}
}

.cus-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: hidden;

	>.el-button {
		margin-bottom: 10px;
	}

	.cus-list {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;
		position: relative;

		.cus-table {
			flex: 1;
			overflow: auto;
			height: 100%;
		}

		.cus-pages {
			margin-top: 10px;
		}
	}
}
</style>
