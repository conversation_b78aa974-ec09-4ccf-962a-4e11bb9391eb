<template>
  <el-drawer :title="form.id ? '编辑故障' : '新增故障'" v-model="dialogVisible" size="650px" :close-on-click-modal="false"
    :before-close="handleClose" direction="rtl">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="drawer-form">
      <el-form-item label="故障名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入故障名称" />
      </el-form-item>

      <el-form-item label="故障等级" prop="level">
        <el-select v-model="form.level" placeholder="请选择故障等级" style="width: 100%">
          <el-option v-for="item in faultLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="工单类型" prop="workOrderType">
        <el-select v-model="form.workOrderType" placeholder="请选择工单类型" style="width: 100%">
          <el-option v-for="item in workOrderTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
      </el-form-item>

      <el-form-item label="是否启用" prop="status">
        <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
      </el-form-item>

      <!-- 故障码列表 - 只在工单类型为fault时显示 -->
      <el-form-item v-if="showFaultCodes" label="故障码列表">
        <div class="fault-code-cards">
          <div v-for="(manufacturer, mIndex) in form.faultCodes" :key="mIndex" class="fault-code-card">
            <!-- 厂家标题和操作按钮 -->
            <div class="manufacturer-header">
              <span class="manufacturer-title">厂家 {{ mIndex + 1 }}</span>
              <div class="manufacturer-actions">
                <el-button type="primary" class="action-btn add-btn"
                  @click="addFaultCodeForManufacturer(manufacturer.manufacturer, mIndex)">添加</el-button>
                <el-button type="danger" class="action-btn delete-btn" @click="removeFaultCode(mIndex)">删除</el-button>
              </div>
            </div>

            <div class="manufacturer-divider"></div>

            <!-- 厂家选择 -->
            <el-form-item label="厂家" :prop="`faultCodes[${mIndex}].manufacturer`"
              :rules="[{ required: true, message: '请选择厂家', trigger: 'change' }]">
              <el-select v-model="manufacturer.manufacturer" placeholder="请选择" style="width: 100%">
                <el-option v-for="item in manufacturerOptions" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>

            <!-- 嵌套的故障码列表 -->
            <div v-for="(faultCode, fIndex) in manufacturer.faultCodes" :key="`${mIndex}-${fIndex}`"
              class="fault-code-item">
              <div class="fault-code-header">
                <span class="fault-code-title">故障码 {{ fIndex + 1 }}</span>
                <el-button type="danger" size="small" circle class="delete-circle-btn"
                  @click="manufacturer.faultCodes.length > 1 ? removeFaultCodeItem(mIndex, fIndex) : ElMessage.warning('至少保留一个故障码')">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </el-button>
              </div>
              <div class="fault-code-divider"></div>

              <div class="fault-code-fields">
                <el-form-item label="故障名称" :prop="`faultCodes[${mIndex}].faultCodes[${fIndex}].description`"
                  :rules="[{ required: true, message: '请输入故障名称', trigger: 'blur' }, { max: 20, message: '故障名称不能超过20个字符', trigger: 'blur' }, { validator: validateNoWhitespace, trigger: 'blur' }]">
                  <el-input v-model="faultCode.description" placeholder="请输入故障名称" class="field-input" />
                </el-form-item>

                <el-form-item label="设备型号" :prop="`faultCodes[${mIndex}].faultCodes[${fIndex}].deviceModel`"
                  :rules="[{ max: 20, message: '设备型号不能超过20个字符', trigger: 'blur' }, { validator: validateNoWhitespace, trigger: 'blur' }]">
                  <el-input v-model="faultCode.deviceModel" placeholder="请输入设备型号" class="field-input" />
                </el-form-item>

                <el-form-item label="故障码" :prop="`faultCodes[${mIndex}].faultCodes[${fIndex}].code`"
                  :rules="[{ required: true, message: '请输入故障码', trigger: 'blur' }, { max: 20, message: '故障码不能超过20个字符', trigger: 'blur' }, { validator: validateNoWhitespace, trigger: 'blur' }]">
                  <el-input v-model="faultCode.code" placeholder="请输入故障码" class="field-input" />
                </el-form-item>
                <el-form-item label="确认次数" :prop="`faultCodes[${mIndex}].faultCodes[${fIndex}].faultConfirmCount`">
                  <el-input-number v-model="faultCode.faultConfirmCount" placeholder="请输入确认次数" class="field-input" />
                </el-form-item>
                <el-form-item label="恢复次数" :prop="`faultCodes[${mIndex}].faultCodes[${fIndex}].recoveryConfirmCount`">
                  <el-input-number v-model="faultCode.recoveryConfirmCount" placeholder="请输入恢复次数" class="field-input" />
                </el-form-item>
              </div>
            </div>
          </div>

          <div v-if="form.faultCodes.length === 0" class="empty-fault-codes">
            <el-button type="primary" @click="addFaultCode">添加故障码</el-button>
          </div>

          <div class="add-more-section">
            <el-button type="primary" plain class="add-manufacturer-btn" @click="addFaultCode">
              <el-icon>
                <Plus />
              </el-icon>
              添加厂家
            </el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div class="drawer-footer">
      <el-button @click="handleClose" :disabled="isSubmitting">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="isSubmitting">确 定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import maintainanceAPI from '@/api/maintainance'
import { validateNoWhitespace } from '@/utils/validateRule'
import { useDictStore } from '@/stores/modules/dict'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

const dialogVisible = ref(false)
const formRef = ref(null)
const dictStore = useDictStore()
const isSubmitting = ref(false);

// 使用computed获取字典数据
const faultLevelOptions = computed(() => dictStore.getDictByType('fault_level'))

const workOrderTypeOptions = computed(() => dictStore.getDictByType('work_order_type'))

const manufacturerOptions = computed(() => dictStore.getDictByType('inverter_manufacturer'))

const form = reactive({
  id: '',
  name: '',
  level: '',
  workOrderType: '',
  description: '',
  status: 1,
  faultCodes: [{
    manufacturer: '',
    faultCodes: [{
      deviceModel: '',
      description: '',
      code: '',
      faultConfirmCount: null,
      recoveryConfirmCount: null
    }]
  }]
})

const rules = reactive({
  name: [
    { required: true, message: '请输入故障名称', trigger: 'blur' },
    { max: 20, message: '故障名称不能超过20个字符', trigger: 'blur' },
    { validator: validateNoWhitespace, trigger: 'blur' }
  ],
  workOrderType: [
    { required: true, message: '请选择工单类型', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择故障等级', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' },
  ],
})

watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val && props.data.id) {
    // 编辑模式，获取详细信息
    getFaultDetail(props.data.id)
  } else if (val) {
    // 新增模式，重置表单
    resetForm()
    // 初始化故障码列表
    form.faultCodes = [{
      manufacturer: '',
      faultCodes: [{
        deviceModel: '',
        description: '',
        code: '',
        faultConfirmCount: null,
        recoveryConfirmCount: null
      }]
    }]
  }
}, { immediate: true })

// 监听工单类型变更
watch(() => form.workOrderType, (newVal) => {
  // 如果工单类型不是fault，则清空故障码列表
  if (newVal !== 'fault') {
    form.faultCodes = []
  } else if (form.faultCodes.length === 0) {
    // 如果工单类型是fault，但故障码列表为空，则初始化故障码列表
    form.faultCodes = [{
      manufacturer: '',
      faultCodes: [{
        deviceModel: '',
        description: '',
        code: '',
        faultConfirmCount: null,
        recoveryConfirmCount: null
      }]
    }]
  }
})

// 获取故障详情
const getFaultDetail = (id) => {
  maintainanceAPI.getFaultCategory({ id }).then(res => {
    if (res.success && res.result) {
      const result = { ...res.result }

      // 将扁平结构的故障码转换为嵌套结构
      if (result.faultCodes && Array.isArray(result.faultCodes) && result.faultCodes.length > 0) {
        // 按厂家分组
        const manufacturerGroups = {}

        result.faultCodes.forEach(faultCode => {
          const manufacturer = faultCode.manufacturer

          if (!manufacturerGroups[manufacturer]) {
            manufacturerGroups[manufacturer] = []
          }

          manufacturerGroups[manufacturer].push({
            deviceModel: faultCode.deviceModel || '',
            description: faultCode.description || '',
            code: faultCode.code || '',
            faultConfirmCount: faultCode.faultConfirmCount,
            recoveryConfirmCount: faultCode.recoveryConfirmCount
          })
        })

        // 转换为嵌套结构
        result.faultCodes = Object.keys(manufacturerGroups).map(manufacturer => ({
          manufacturer,
          faultCodes: manufacturerGroups[manufacturer]
        }))
      }

      // 应用转换后的数据到表单
      Object.assign(form, result)

      // 确保故障码列表存在并且格式正确
      if (!form.faultCodes || !Array.isArray(form.faultCodes) || form.faultCodes.length === 0) {
        form.faultCodes = [{
          manufacturer: '',
          faultCodes: [{
            deviceModel: '',
            description: '',
            code: '',
            faultConfirmCount: null,
            recoveryConfirmCount: null
          }]
        }]
      } else {
        // 确保每个厂家都有faultCodes数组
        form.faultCodes.forEach(item => {
          if (!item.faultCodes || !Array.isArray(item.faultCodes) || item.faultCodes.length === 0) {
            item.faultCodes = [{
              deviceModel: '',
              description: '',
              code: '',
              faultConfirmCount: null,
              recoveryConfirmCount: null
            }]
          }
        })
      }
    } else {
      ElMessage.error(res.error || '获取故障详情失败')
    }
  }).catch(error => {
    console.error('获取故障详情失败:', error)
    ElMessage.error('获取故障详情失败')
  })
}

// 添加新厂家
const addFaultCode = () => {
  form.faultCodes.push({
    manufacturer: '',
    faultCodes: [{
      deviceModel: '',
      description: '',
      code: '',
      faultConfirmCount: null,
      recoveryConfirmCount: null
    }]
  })
}

const addFaultCodeForManufacturer = (manufacturer, index) => {
  if (!manufacturer) {
    ElMessage.warning('请先选择厂家')
    return
  }

  if (form.faultCodes[index] && form.faultCodes[index].faultCodes) {
    form.faultCodes[index].faultCodes.push({
      deviceModel: '',
      description: '',
      code: '',
      faultConfirmCount: null,
      recoveryConfirmCount: null
    })
  }
}

const removeFaultCode = (index) => {
  if (form.faultCodes.length > 1) {
    form.faultCodes.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个厂家')
  }
}

const removeFaultCodeItem = (manufacturerIndex, codeIndex) => {
  if (form.faultCodes[manufacturerIndex] && form.faultCodes[manufacturerIndex].faultCodes) {
    if (form.faultCodes[manufacturerIndex].faultCodes.length > 1) {
      form.faultCodes[manufacturerIndex].faultCodes.splice(codeIndex, 1)
    } else {
      ElMessage.warning('至少保留一个故障码')
    }
  }
}

const showFaultCodes = computed(() => {
  return form.workOrderType === 'fault'
})

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return;
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    isSubmitting.value = true;

    const submitData = { ...form }

    if (form.workOrderType !== 'fault') {
      submitData.faultCodes = []
    }

    const apiMethod = form.id ? maintainanceAPI.editFaultCategory : maintainanceAPI.addFaultCategory

    const flattenedFaultCodes = form.workOrderType === 'fault' ? form.faultCodes.flatMap(manufacturer => {
      return manufacturer.faultCodes.map(faultCode => ({
        manufacturer: manufacturer.manufacturer,
        deviceModel: faultCode.deviceModel,
        description: faultCode.description,
        code: faultCode.code,
        faultConfirmCount: faultCode.faultConfirmCount,
        recoveryConfirmCount: faultCode.recoveryConfirmCount
      }))
    }) : []

    const res = await apiMethod({
      ...props.data,
      ...submitData,
      faultCodes: flattenedFaultCodes
    })

    if (res.data.success) {
      ElMessage.success('操作成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res.data.error || '操作失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.warning('请完善表单信息')
  } finally {
    isSubmitting.value = false; // Reset submitting state
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    name: '',
    level: '',
    workOrderType: '',
    description: '',
    status: 1,
    faultCodes: [{
      manufacturer: '',
      faultCodes: [{
        deviceModel: '',
        description: '',
        code: '',
        faultConfirmCount: null,
        recoveryConfirmCount: null
      }]
    }]
  })
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  resetForm()
}
</script>

<style lang="less" scoped>
.fault-code-cards {
  width: 100%;

  .fault-code-card {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 24px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    padding-bottom: 2px;

    .manufacturer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 16px;
      background-color: #f8f9fa;

      .manufacturer-title {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .manufacturer-actions {
        .action-btn {
          margin-left: 8px;
          border-radius: 4px;
          padding: 8px 16px;
          font-size: 14px;

          &.add-btn {
            background-color: #409EFF;
            border-color: #409EFF;

            &:hover {
              background-color: #66b1ff;
              border-color: #66b1ff;
            }
          }

          &.delete-btn {
            background-color: #f56c6c;
            border-color: #f56c6c;

            &:hover {
              background-color: #f78989;
              border-color: #f78989;
            }
          }
        }
      }
    }

    .manufacturer-divider {
      height: 1px;
      background-color: #ebeef5;
      margin: 0;
      margin-bottom: 18px;
    }

    .el-form-item {
      padding: 0 16px;
      margin-bottom: 18px;

      :deep(.el-form-item__label) {
        width: 100px;
        text-align: right;
        padding-right: 12px;
        color: #606266;
        font-size: 14px;
      }

      :deep(.el-input__wrapper) {
        border-radius: 4px;
        padding: 1px 15px;
        box-shadow: 0 0 0 1px #dcdfe6 inset;

        &:hover {
          box-shadow: 0 0 0 1px #c0c4cc inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409EFF inset;
        }
      }
    }


    .fault-code-item {
      background-color: #f9f9f9;
      border-radius: 8px;
      margin: 0 16px 12px;
      padding: 15px;
      border: 1px solid #ebeef5;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      &:last-child {
        margin-bottom: 12px;
      }

      .fault-code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 0;

        .fault-code-title {
          font-size: 15px;
          font-weight: 500;
          color: #303133;
        }
      }

      .fault-code-divider {
        height: 1px;
        background-color: #e0e0e0;
        margin: 0 0 12px;
        border-top: 1px dashed #e0e0e0;
      }

      .delete-circle-btn {
        padding: 6px;
        transition: all 0.2s ease;
        background-color: rgba(245, 108, 108, 0.1);
        border-color: rgba(245, 108, 108, 0.2);
        color: #f56c6c;

        &:hover {
          background-color: #f56c6c;
          border-color: #f56c6c;
          color: white;
        }

        i {
          font-size: 12px;
        }
      }

      .fault-code-fields {
        padding: 0;

        .el-form-item {
          padding: 0;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .empty-fault-codes {
    text-align: center;
    padding: 24px 0;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .add-more-section {
    text-align: center;
    margin: 24px 0;

    .add-manufacturer-btn {
      width: 240px;
      border-radius: 4px;
      color: #409EFF;
      border-color: #d9ecff;
      background-color: #ecf5ff;
      padding: 10px 20px;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);

      &:hover {
        background-color: #d9ecff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
      }

      :deep(.el-icon) {
        margin-right: 8px;
        font-size: 16px;
        vertical-align: middle;
      }
    }
  }
}

.drawer-form {
  height: calc(100% - 80px);
  overflow-y: auto;
  padding-right: 12px;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}
</style>
